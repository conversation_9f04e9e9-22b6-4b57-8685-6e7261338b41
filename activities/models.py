from django.db import models
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey


class ActivityType(models.TextChoices):
    """Defines the types of activities that can be logged"""
    CREATED = "created", "Created"
    UPDATED = "updated", "Updated"
    STATUS_CHANGED = "status_changed", "Status Changed"
    SENT_TO_DEPARTMENT = "sent_to_department", "Sent to Department"
    SENT_FROM_DEPARTMENT = "sent_from_department", "Sent from Department"
    DOCUMENT_ADDED = "document_added", "Document Added"
    DOCUMENT_REMOVED = "document_removed", "Document Removed"
    REVIEW_ADDED = "review_added", "Review Added"
    ASSIGNED = "assigned", "Assigned"
    UNASSIGNED = "unassigned", "Unassigned"
    RESOLVED = "resolved", "Resolved"
    REOPENED = "reopened", "Reopened"
    INVESTIGATION_STARTED = "investigation_started", "Investigation Started"
    INVESTIGATION_COMPLETED = "investigation_completed", "Investigation Completed"
    COMMENT_ADDED = "comment_added", "Comment Added"
    NOTIFICATION_SENT = "notification_sent", "Notification Sent"
    WORKFLOW_STEP_COMPLETED = "workflow_step_completed", "Workflow Step Completed"


class ActivityLog(models.Model):
    """
    Enhanced activity log model that tracks all activities across different incident types.
    Uses Django's ContentType framework to support any incident model.
    """

    # User who performed the action
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="activity_logs",
        null=True,
        blank=True,
    )

    # Activity details
    activity_type = models.CharField(
        max_length=50,
        choices=ActivityType.choices,
        default=ActivityType.UPDATED,
    )

    # Legacy field for backward compatibility
    action = models.CharField(max_length=50, blank=True, null=True)

    # Timestamp
    timestamp = models.DateTimeField(auto_now_add=True)

    # Request metadata
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.CharField(max_length=255, null=True, blank=True)

    # Activity description and details
    description = models.TextField(null=True, blank=True)
    details = models.JSONField(default=dict, blank=True)  # Store additional metadata

    # Generic foreign key to support any incident type
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    # Legacy field for backward compatibility
    incident_id = models.CharField(max_length=255, null=True, blank=True)

    # Additional context fields
    department = models.ForeignKey(
        'base.Department',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Department involved in the activity"
    )

    facility = models.ForeignKey(
        'base.Facility',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Facility where the activity occurred"
    )

    # For activities involving other users (assignments, notifications, etc.)
    target_user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="targeted_activities",
        help_text="User who is the target of this activity (e.g., assigned user)"
    )

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['activity_type']),
            models.Index(fields=['timestamp']),
            models.Index(fields=['user']),
        ]

    def __str__(self):
        return f"{self.user} - {self.get_activity_type_display()} - {self.content_object} at {self.timestamp}"

    def get_formatted_description(self):
        """
        Returns a formatted description based on activity type and details.
        This matches the format shown in the design.
        """
        if self.description:
            return self.description

        # Generate description based on activity type
        if self.activity_type == ActivityType.SENT_TO_DEPARTMENT:
            dept_name = self.details.get('department_name', 'Unknown Department')
            return f"Incident sent to {dept_name}"
        elif self.activity_type == ActivityType.SENT_FROM_DEPARTMENT:
            dept_name = self.details.get('department_name', 'Unknown Department')
            return f"Incident sent from {dept_name}"
        elif self.activity_type == ActivityType.DOCUMENT_ADDED:
            doc_count = self.details.get('document_count', 1)
            if doc_count == 1:
                return "1 incoming document"
            else:
                return f"{doc_count} incoming documents"
        elif self.activity_type == ActivityType.REVIEW_ADDED:
            return "New Review"
        elif self.activity_type == ActivityType.STATUS_CHANGED:
            old_status = self.details.get('old_status', '')
            new_status = self.details.get('new_status', '')
            return f"Status changed from {old_status} to {new_status}"
        elif self.activity_type == ActivityType.ASSIGNED:
            assignee = self.details.get('assignee_name', 'Unknown')
            return f"Assigned to {assignee}"
        else:
            return f"{self.get_activity_type_display()}"

    def get_activity_icon(self):
        """Returns the appropriate icon for the activity type"""
        icon_mapping = {
            ActivityType.CREATED: "plus-circle",
            ActivityType.UPDATED: "edit",
            ActivityType.STATUS_CHANGED: "refresh",
            ActivityType.SENT_TO_DEPARTMENT: "arrow-right",
            ActivityType.SENT_FROM_DEPARTMENT: "arrow-left",
            ActivityType.DOCUMENT_ADDED: "file-plus",
            ActivityType.DOCUMENT_REMOVED: "file-minus",
            ActivityType.REVIEW_ADDED: "message-square",
            ActivityType.ASSIGNED: "user-plus",
            ActivityType.UNASSIGNED: "user-minus",
            ActivityType.RESOLVED: "check-circle",
            ActivityType.REOPENED: "rotate-ccw",
            ActivityType.INVESTIGATION_STARTED: "search",
            ActivityType.INVESTIGATION_COMPLETED: "check-square",
            ActivityType.COMMENT_ADDED: "message-circle",
            ActivityType.NOTIFICATION_SENT: "bell",
            ActivityType.WORKFLOW_STEP_COMPLETED: "check",
        }
        return icon_mapping.get(self.activity_type, "activity")
